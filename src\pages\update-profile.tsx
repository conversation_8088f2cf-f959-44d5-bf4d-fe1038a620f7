import { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { getAuth } from "firebase/auth";
import { getFirestore, doc, getDoc, updateDoc, setDoc, deleteDoc } from "firebase/firestore";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectItem,
} from "@/components/ui/select";
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
} from "@/components/ui/command";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { Badge } from "@/components/ui/badge";
import { Check, ChevronsUpDown, X, Plus, Trash2, Edit, Link as LinkIcon, Copy as CopyIcon, Crown, Shield, Camera, Upload, Image as ImageIcon } from "lucide-react";
import { cn } from "@/lib/utils";
import { CountrySelect } from "@/components/ui/country-select";
import { useFormValidation } from "@/hooks/useFormValidation";
import { useAllUsers, useInvalidateUserCache } from "@/hooks/useFirebaseCache";
import { toast, ToastContainer } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";

// Tech stack options
const techStackOptions = [
  // Frontend Frameworks & Libraries
  "React",
  "Next.js",
  "Vue.js",
  "Nuxt.js",
  "Angular",
  "Svelte",
  "SolidJS",
  "Alpine.js",
  "jQuery",
  "SvelteKit",
  "Astro",
  "SolidStart",
  "Qwik",
  "Remix",

  // Backend Frameworks
  "Node.js",
  "Express",
  "NestJS",
  "Fastify",
  "Django",
  "Flask",
  "Ruby on Rails",
  "Spring Boot",
  "ASP.NET",

  // Languages
  "JavaScript",
  "TypeScript",
  "Python",
  "Java",
  "C#",
  "C",
  "C++",
  "Go",
  "Rust",
  "PHP",
  "Swift",
  "Kotlin",
  "Dart",
  "Bash",
  "SQL",
  "R",

  // Styling
  "TailwindCSS",
  "SCSS",
  "CSS",
  "HTML",
  "Less",
  "Bootstrap",
  "Material UI",
  "Chakra UI",

  // Databases
  "MongoDB",
  "PostgreSQL",
  "MySQL",
  "SQLite",
  "Firebase",
  "Supabase",
  "Redis",
  "Oracle",
  "Microsoft SQL Server",

  // Cloud & DevOps
  "AWS",
  "Azure",
  "Google Cloud",
  "Docker",
  "Kubernetes",
  "Vercel",
  "Netlify",
  "Heroku",
  "DigitalOcean",
  "Nginx",
  "Apache",

  // Version Control & Collaboration
  "Git",
  "GitHub",
  "GitLab",
  "Bitbucket",

  // Design & Prototyping
  "Figma",
  "Adobe XD",
  "Photoshop",
  "Illustrator",
  "After Effects",
  "Sketch",
  "InVision",

  // APIs & Data Handling
  "GraphQL",
  "REST",
  "gRPC",
  "WebSockets",
  "Apollo",
  "Axios",
  "Postman",

  // Testing
  "Jest",
  "Mocha",
  "Chai",
  "Cypress",
  "Playwright",
  "Testing Library",
  "Selenium",

  // Mobile Development
  "React Native",
  "Flutter",
  "Ionic",
  "SwiftUI",
  "Xamarin",

  // Build Tools & Package Managers
  "Webpack",
  "Vite",
  "Parcel",
  "Rollup",
  "Babel",
  "NPM",
  "Yarn",
  "PNPM",

  // CI/CD & Automation
  "GitHub Actions",
  "GitLab CI",
  "CircleCI",
  "Travis CI",
  "Jenkins",

  // Miscellaneous
  "Linux",
  "Command Line",
  "Markdown",
  "WebAssembly",
  "Electron",
  "Three.js",
  "Unity",
  "Blender",
];

const roleOptions = [
  "Frontend Developer",
  "Backend Developer",
  "MERN Developer",
  "Full-Stack Developer",
  "Mobile App Developer",
  "DevOps Engineer",
  "Software Engineer",
  "QA Engineer / Tester",
  "Game Developer",
  "Data Scientist / Data Analyst",
  "Machine Learning Engineer",
];

const seniorityOptions = [
  "Junior",
  "Mid-Level",
  "Senior",
  "Lead",
  "Principal",
  "Architect",
  "Other",
];

interface Project {
  id: string;
  title: string;
  description: string;
  url: string;
  imageUrl: string;
  technologies: string[];
}

interface ValidationError {
  field: string;
  message: string;
  type: "error" | "success";
}

interface FormData {
  displayName: string;
  username: string;
  bio: string;
  country: string;
  role: string;
  seniorityLevel: string;
  website: string;
  githubUrl: string;
  twitterUrl: string;
  linkedinUrl: string;
  techStack: string[];
  projects: Project[];
  bannerURL: string;
  photoURL: string;
  usernameChangesCount?: number;
  availableForWork?: boolean;
  workType?: 'freelance' | 'full-time' | 'none';
  hourlyRate?: string;
}

interface BannerOption {
  id: string;
  url: string;
  name: string;
  freeAccess: boolean;
}

const bannerOptions: BannerOption[] = [
  {
    id: "code",
    url: "/photos/code.jpg",
    name: "Code",
    freeAccess: true,
  },
  {
    id: "futuristic-laptop",
    url: "/photos/a-futuristic-laptop-displays.jpg",
    name: "Futuristic Laptop",
    freeAccess: true,
  },
  {
    id: "programming-desk",
    url: "/photos/programming-desk.jpg",
    name: "Programming Desk",
    freeAccess: true,
  },
  {
    id: "wallpaper",
    url: "/photos/wallpaper.jpg",
    name: "Wallpaper",
    freeAccess: true,
  },
  {
    id: "wallpaper",
    url: "https://img.freepik.com/premium-photo/matt-furie-drawing-sad-crypto-trader_950002-568877.jpg",
    name: "Wallpaper",
    freeAccess: true,
  },
  {
    id: "wallpaper",
    url: "https://img.freepik.com/free-photo/beautiful-office-space-cartoon-style_23-2151043334.jpg",
    name: "Wallpaper",
    freeAccess: true,
  },

];

const premiumBannerOptions: BannerOption[] = [
  {
    id: "devloper",
    url: "https://media0.giphy.com/media/v1.Y2lkPTc5MGI3NjExcXNibThzaWhpcThpZWJzbzBoYTZ1NWdrYWtoNjUyZHI1NjdmN2UzZyZlcD12MV9pbnRlcm5hbF9naWZfYnlfaWQmY3Q9Zw/f3KwliaH4MLtli8z7D/giphy.gif",
    name: "devloper",
    freeAccess: false,
  },
  {
    id: "time",
    url: "https://media2.giphy.com/media/v1.Y2lkPTc5MGI3NjExZm40bW14bzJqNGVreXE2eTg2Mm50ZmdzNXU5N3p2Y2ZmMTNwbmM3diZlcD12MV9pbnRlcm5hbF9naWZfYnlfaWQmY3Q9Zw/Z78fjdFpO4EeZTcG6r/giphy.gif",
    name: "Time is Money",
    freeAccess: false,
  },
  {
    id: "code_is_life",
    url: "https://media3.giphy.com/media/v1.Y2lkPTc5MGI3NjExaHg4OTc2MTNhc28zM3Ayam9hdWVmNml2bTlqeDhyZzNsYTl4c2VnayZlcD12MV9pbnRlcm5hbF9naWZfYnlfaWQmY3Q9Zw/ZVik7pBtu9dNS/giphy.gif",
    name: "Code is Life",
    freeAccess: false,
  },
  {
    id: "do_code",
    url: "https://media0.giphy.com/media/v1.Y2lkPTc5MGI3NjExdmI3bnc5OXFyYW5pamM0dnp2M3FkYzgwbW81Z3l0OXBkMGlhZGpwdiZlcD12MV9pbnRlcm5hbF9naWZfYnlfaWQmY3Q9Zw/RbDKaczqWovIugyJmW/giphy.gif",
    name: "Best coder",
    freeAccess: false,
  },
  {
    id: "24hr_code",
    url: "https://media0.giphy.com/media/v1.Y2lkPTc5MGI3NjExZG50dGJ2ZWM3ZDc5ZzA3dHgzdXl0bTdmMHpmM2V1angzdXkwb3RyayZlcD12MV9pbnRlcm5hbF9naWZfYnlfaWQmY3Q9Zw/13HgwGsXF0aiGY/giphy.gif",
    name: "24hr code",
    freeAccess: false,
  },
  {
    id: "cat_is_free",
    url: "https://media2.giphy.com/media/v1.Y2lkPTc5MGI3NjExcWljdzlxMnlxN3E1ZGVvcmVjNGI4anZuMmloOGNpc3o4ZHhxZnY2MyZlcD12MV9pbnRlcm5hbF9naWZfYnlfaWQmY3Q9Zw/6RuhlzSdhIAqk/giphy.gif",
    name: "Cat is free",
    freeAccess: false,
  },
  {
    id: "hacker_one",
    url: "https://media0.giphy.com/media/v1.Y2lkPTc5MGI3NjExYjZieDFxc2VsbXltOXIzdnBvcjM2a2I5ZWxlOHIxZGtxcHA0ZnRyYiZlcD12MV9pbnRlcm5hbF9naWZfYnlfaWQmY3Q9Zw/YQitE4YNQNahy/giphy.gif",
    name: "Hacker",
    freeAccess: false,
  },
  {
    id: "two_hacker",
    url: "https://media4.giphy.com/media/v1.Y2lkPTc5MGI3NjExeW11czN2eDczamRmbjQxNzY1Z2VxNmYyb216MnZydmtsejRxaGNwMyZlcD12MV9pbnRlcm5hbF9naWZfYnlfaWQmY3Q9Zw/MD0svLSDeudszrNrp0/giphy.gif",
    name: "Hacker black",
    freeAccess: false,
  },
  {
    id: "hollywood",
    url: "https://media2.giphy.com/media/v1.Y2lkPTc5MGI3NjExaXFzOGI5NXEyeDloYzZ4NW96NXZuM3RmeGozOWpxdWZ0NjhlYWhzcyZlcD12MV9pbnRlcm5hbF9naWZfYnlfaWQmY3Q9Zw/L3ERvA6jWCd0qO4NdX/giphy.gif",
    name: "Hollywood",
    freeAccess: false,
  },
  {
    id: "hacker",
    url: "https://media2.giphy.com/media/v1.Y2lkPTc5MGI3NjExbndyOGU1emhibWFyZWk0OXVteWRoMTVoMGE0am53ZmJlMjg2bnpnMyZlcD12MV9pbnRlcm5hbF9naWZfYnlfaWQmY3Q9Zw/d3mlE7uhX8KFgEmY/giphy.gif",
    name: "Hacker black",
    freeAccess: false,
  },
  {
    id: "money",
    url: "https://media0.giphy.com/media/v1.Y2lkPTc5MGI3NjExa253Y253OXZib3FlcG53b3IxOXFpNDB5dzN3Yjc5amU1MzU4dmtjciZlcD12MV9pbnRlcm5hbF9naWZfYnlfaWQmY3Q9Zw/XGP7mf38Vggik/giphy.gif",
    name: "Money",
    freeAccess: false,
  },
  {
    id: "nature",
    url: "https://media0.giphy.com/media/v1.Y2lkPTc5MGI3NjExbDB1amFtN2syZGJ2YXI1MmtnbnFxdDZ4djBvaDR3b3czdTNjOGhyayZlcD12MV9pbnRlcm5hbF9naWZfYnlfaWQmY3Q9Zw/dU97uV3UyP0ly/giphy.gif",
    name: "Nature",
    freeAccess: false,
  },
];

export default function UpdateProfile() {
  const [loading, setLoading] = useState(false);
  const [showProjectForm, setShowProjectForm] = useState(false);
  const [editingProjectId, setEditingProjectId] = useState<string | null>(null);
  const [errors, setErrors] = useState<ValidationError[]>([]);
  const [currentProject, setCurrentProject] = useState<Project>({
    id: "",
    title: "",
    description: "",
    url: "",
    imageUrl: "",
    technologies: [],
  });
  const [projectTechStackOpen, setProjectTechStackOpen] = useState(false);
  const [usernameTimeout, setUsernameTimeout] = useState<NodeJS.Timeout | null>(
    null
  );
  const [isFeatured, setIsFeatured] = useState(false);
  const [isVerified, setIsVerified] = useState(false);
  const [isTypingUsername, setIsTypingUsername] = useState(false);
  const [usernameChangesCount, setUsernameChangesCount] = useState(0);
  const [initialUsername, setInitialUsername] = useState("");
  const [showPhotoModal, setShowPhotoModal] = useState(false);
  const [photoUrl, setPhotoUrl] = useState("");
  const [isUpdatingPhoto, setIsUpdatingPhoto] = useState(false);

  // Use cached data instead of local state
  const { data: existingUsers = [] } = useAllUsers();
  const invalidateCache = useInvalidateUserCache();

  const {
    formData,
    setFormData,
    isValidating,
    handleChange,
    handleSelectChange,
    handleTechStackChange,
    validateAll,
    clearError,
  } = useFormValidation<FormData>(
    {
      displayName: "",
      username: "",
      bio: "",
      country: "",
      role: "",
      seniorityLevel: "",
      website: "",
      githubUrl: "",
      twitterUrl: "",
      linkedinUrl: "",
      techStack: [],
      projects: [],
      bannerURL: "/photos/programming-desk.jpg",
      photoURL: "",
    },
    existingUsers as any
  );

  const navigate = useNavigate();
  const auth = getAuth();
  const db = getFirestore();

  // Users are now fetched via cached hook above

  // Fetch and load user's existing profile data
  useEffect(() => {
    const loadUserProfile = async () => {
      const user = auth.currentUser;
      if (!user) {
        navigate("/");
        return;
      }

      try {
        const userRef = doc(db, "users", user.uid);
        const snapshot = await getDoc(userRef);

        if (snapshot.exists()) {
          const userData = snapshot.data();
          setIsFeatured(userData.isFeatured || false);
          setIsVerified(userData.isVerified || false);
          setUsernameChangesCount(userData.usernameChangesCount || 0);
          setInitialUsername(userData.username || "");

          // Format social media URLs to remove domain
          const githubUsername =
            userData.githubUrl?.replace("https://github.com/", "") || "";
          const twitterUsername =
            userData.twitterUrl?.replace("https://twitter.com/", "") || "";
          const linkedinUsername =
            userData.linkedinUrl?.replace("https://linkedin.com/in/", "") || "";

          setFormData({
            displayName: userData.displayName || "",
            username: userData.username || "",
            bio: userData.bio || "",
            country: userData.country || "",
            role: userData.role || "",
            seniorityLevel: userData.seniorityLevel || "",
            website: userData.website || "",
            githubUrl: githubUsername,
            twitterUrl: twitterUsername,
            linkedinUrl: linkedinUsername,
            techStack: userData.techStack || [],
            projects: (userData.projects || []).map((project: any, index: number) => ({
              ...project,
              id: project.id || `legacy_project_${Date.now()}_${index}`,
              imageUrl: project.imageUrl || "",
              technologies: project.technologies || [],
            })),
            bannerURL: userData.bannerURL || "/photos/programming-desk.jpg",
            photoURL: userData.photoURL || "",
            usernameChangesCount: userData.usernameChangesCount || 0,
            availableForWork: userData.availableForWork || false,
            workType: userData.workType || 'freelance',
            hourlyRate: userData.hourlyRate || '',
          });
        } else {
          navigate("/create-profile");
        }
      } catch (error) {
        // Handle error silently
      }
    };

    const unsubscribe = auth.onAuthStateChanged((user) => {
      if (user) {
        loadUserProfile();
      } else {
        navigate("/");
      }
    });

    return () => unsubscribe();
  }, [auth, db, navigate, setFormData]);

  // Username validation with change limit check
  useEffect(() => {
    if (!isTypingUsername) return;

    if (usernameTimeout) {
      clearTimeout(usernameTimeout);
    }

    const timeout = setTimeout(() => {
      if (!formData.username) {
        setErrors((prev) => prev.filter((err) => err.field !== "username"));
        return;
      }

      const username = formData.username.toLowerCase();

      // Check if username is different from initial
      if (username === initialUsername.toLowerCase()) {
        setErrors((prev) => prev.filter((err) => err.field !== "username"));
        return;
      }

      // Check username change limit for non-featured users
      if (!isFeatured && usernameChangesCount >= 2) {
        setErrors((prev) => [
          ...prev.filter((err) => err.field !== "username"),
          {
            field: "username",
            message:
              "You've reached the maximum number of username changes. Upgrade to Featured Developer for unlimited changes.",
            type: "error",
          },
        ]);
        return;
      }

      const isUsernameTaken = existingUsers.some(
        (user) =>
          user.username?.toLowerCase() === username &&
          user.uid !== auth.currentUser?.uid
      );

      if (isUsernameTaken) {
        setErrors((prev) => [
          ...prev.filter((err) => err.field !== "username"),
          {
            field: "username",
            message: "This username is already taken",
            type: "error",
          },
        ]);
      } else if (username.length >= 3) {
        setErrors((prev) => [
          ...prev.filter((err) => err.field !== "username"),
          {
            field: "username",
            message: isFeatured
              ? "Username is available"
              : `Username is available (${
                  2 - usernameChangesCount
                } changes remaining)`,
            type: "success",
          },
        ]);
      } else {
        setErrors((prev) => prev.filter((err) => err.field !== "username"));
      }
    }, 500);

    setUsernameTimeout(timeout);

    return () => {
      if (usernameTimeout) {
        clearTimeout(usernameTimeout);
      }
    };
  }, [
    formData.username,
    existingUsers,
    auth.currentUser?.uid,
    isTypingUsername,
    isFeatured,
    usernameChangesCount,
    initialUsername,
  ]);

  // Custom handle change to track username typing
  const handleUsernameChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (!isTypingUsername) {
      setIsTypingUsername(true);
    }
    handleChange(e);
  };

  // Prevent form submission on Enter key
  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === "Enter") {
      e.preventDefault();
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    const isValid = await validateAll();

    const hasErrors = errors.some((error) => error.type === "error");
    if (hasErrors) {
      toast.error("Please fix the errors in the form before submitting");
      return;
    }

    // Check username change limit for non-featured users
    if (
      !isFeatured &&
      usernameChangesCount >= 2 &&
      formData.username.toLowerCase() !== initialUsername.toLowerCase()
    ) {
      toast.error(
        "You've reached the maximum number of username changes. Upgrade to Featured Developer for unlimited changes."
      );
      return;
    }

    setLoading(true);

    try {
      const user = auth.currentUser;
      if (!user) throw new Error("No user found");

      // Format social media URLs
      const formattedData = {
        ...formData,
        githubUrl: formData.githubUrl
          ? `https://github.com/${formData.githubUrl}`
          : "",
        twitterUrl: formData.twitterUrl
          ? `https://twitter.com/${formData.twitterUrl}`
          : "",
        linkedinUrl: formData.linkedinUrl
          ? `https://linkedin.com/in/${formData.linkedinUrl}`
          : "",
        updatedAt: Date.now(),
      };

      // Update username changes count if username was changed
      if (formData.username.toLowerCase() !== initialUsername.toLowerCase()) {
        formattedData.usernameChangesCount = (usernameChangesCount || 0) + 1;
      }

      console.log("Updating user profile with data:", formattedData);

      // Update user profile in database
      await updateDoc(doc(db, "users", user.uid), formattedData);

      // If username changed, update username reservation
      if (formData.username !== initialUsername) {
        // Remove old username reservation
        await deleteDoc(doc(db, "usernames", initialUsername.toLowerCase()));

        // Add new username reservation
        await setDoc(doc(db, "usernames", formData.username.toLowerCase()), {
          uid: user.uid,
        });
      }

      console.log("Profile updated successfully");

      // Invalidate cache to refresh data across the app
      await invalidateCache.mutateAsync();

      toast.success(
        "🎉 Profile updated successfully! Redirecting to portfolios..."
      );

      // Small delay before redirect to ensure toast is visible
      setTimeout(() => {
        navigate("/portfolios");
      }, 2000);
    } catch (error) {
      console.error("Error updating profile:", error);
      toast.error("Failed to update profile. Please try again.");
    } finally {
      setLoading(false);
    }
  };

  const removeTechStack = (value: string) => {
    setFormData((prev: FormData) => ({
      ...prev,
      techStack: prev.techStack.filter((item: string) => item !== value),
    }));
  };

  const handleProjectChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
  ) => {
    const { name, value } = e.target;
    setCurrentProject((prev: Project) => ({ ...prev, [name]: value }));
  };

  const handleProjectTechStackChange = (value: string) => {
    setCurrentProject((prev: Project) => {
      const currentTechnologies = prev.technologies || [];
      return {
        ...prev,
        technologies: currentTechnologies.includes(value)
          ? currentTechnologies.filter((item: string) => item !== value)
          : [...currentTechnologies, value],
      };
    });
  };

  const removeProjectTech = (value: string) => {
    setCurrentProject((prev: Project) => ({
      ...prev,
      technologies: (prev.technologies || []).filter((item: string) => item !== value),
    }));
  };

  const validateProjects = (projects: Project[]): ValidationError | null => {
    if (projects.length > 5) {
      return {
        field: "projects",
        message: "You can add up to 5 projects",
        type: "error",
      };
    }
    return null;
  };

  const addProject = () => {
    if (!currentProject.title || !currentProject.description) return;

    if (editingProjectId) {
      // Update existing project
      setFormData((prev: FormData) => {
        const updatedProjects = prev.projects.map((project) =>
          project.id === editingProjectId ? { ...currentProject } : project
        );

        const projectsError = validateProjects(updatedProjects);
        if (projectsError) {
          setErrors((prev) => [
            ...prev.filter((err) => err.field !== "projects"),
            projectsError,
          ]);
        } else {
          setErrors((prev) => prev.filter((err) => err.field !== "projects"));
        }

        return {
          ...prev,
          projects: updatedProjects,
        };
      });
    } else {
      // Add new project
      setFormData((prev: FormData) => {
        const newProjects = [
          ...prev.projects,
          { ...currentProject, id: `project_${Date.now()}_${Math.random().toString(36).substring(2, 11)}` },
        ];

        const projectsError = validateProjects(newProjects);
        if (projectsError) {
          setErrors((prev) => [
            ...prev.filter((err) => err.field !== "projects"),
            projectsError,
          ]);
        } else {
          setErrors((prev) => prev.filter((err) => err.field !== "projects"));
        }

        return {
          ...prev,
          projects: newProjects,
        };
      });
    }

    setCurrentProject({
      id: "",
      title: "",
      description: "",
      url: "",
      imageUrl: "",
      technologies: [],
    });
    setEditingProjectId(null);
    setShowProjectForm(false);
  };

  const editProject = (project: Project) => {
    setCurrentProject({
      ...project,
      technologies: project.technologies || [], // Ensure technologies is always an array
    });
    setEditingProjectId(project.id);
    setShowProjectForm(true);
  };

  const removeProject = (projectId: string) => {
    const projectToDelete = formData.projects.find(p => p.id === projectId);

    if (!projectToDelete) {
      toast.error("Project not found!");
      return;
    }

    // Show confirmation dialog
    const confirmed = window.confirm(
      `Are you sure you want to delete "${projectToDelete.title}"?\n\nThis action cannot be undone.`
    );

    if (!confirmed) {
      return;
    }

    console.log("Removing project with ID:", projectId);
    console.log("Current projects:", formData.projects);

    setFormData((prev: FormData) => {
      const filteredProjects = prev.projects.filter(
        (project: Project) => project.id !== projectId
      );
      console.log("Projects after filtering:", filteredProjects);
      console.log("Removed project:", projectToDelete.title);

      return {
        ...prev,
        projects: filteredProjects,
      };
    });

    toast.success(`"${projectToDelete.title}" deleted successfully!`);
  };

  const getFieldError = (field: string) => {
    return errors.find((error) => error.field === field);
  };

  const handleBannerSelect = (banner: BannerOption) => {
    setFormData((prev) => ({ ...prev, bannerURL: banner.url }));
    clearError("bannerURL");
  };

  const handlePhotoUpdate = async () => {
    if (!photoUrl.trim()) {
      toast.error("Please enter a valid image URL");
      return;
    }

    // Basic URL validation
    try {
      new URL(photoUrl);
    } catch {
      toast.error("Please enter a valid URL");
      return;
    }

    setIsUpdatingPhoto(true);

    try {
      // Test if the image loads
      const img = new Image();
      img.onload = async () => {
        try {
          const user = auth.currentUser;
          if (!user) throw new Error("No user found");

          // Update the profile photo URL
          await updateDoc(doc(db, "users", user.uid), {
            photoURL: photoUrl,
            updatedAt: Date.now(),
          });

          // Update local state
          setFormData((prev) => ({ ...prev, photoURL: photoUrl }));

          // Invalidate cache to refresh data across the app
          await invalidateCache.mutateAsync();

          toast.success("🎉 Profile photo updated successfully!");
          setShowPhotoModal(false);
          setPhotoUrl("");
        } catch (error) {
          console.error("Error updating profile photo:", error);
          toast.error("Failed to update profile photo. Please try again.");
        } finally {
          setIsUpdatingPhoto(false);
        }
      };

      img.onerror = () => {
        toast.error("Invalid image URL. Please check the URL and try again.");
        setIsUpdatingPhoto(false);
      };

      img.src = photoUrl;
    } catch (error) {
      toast.error("Invalid URL format");
      setIsUpdatingPhoto(false);
    }
  };

  return (
    <div className="container max-w-2xl mx-auto px-4 py-8">
      <ToastContainer
        position="top-right"
        autoClose={3000}
        hideProgressBar={false}
        newestOnTop
        closeOnClick
        rtl={false}
        pauseOnFocusLoss
        draggable
        pauseOnHover
        theme="light"
      />
      
      {/* Photo Update Modal */}
      {showPhotoModal && (
        <div className="fixed inset-0 bg-black/50 backdrop-blur-sm z-[9999] flex items-center justify-center p-4">
          <div className="bg-white dark:bg-gray-900 rounded-2xl shadow-2xl max-w-md w-full p-6 space-y-6 border border-gray-200 dark:border-gray-700">
            <div className="flex items-center justify-between">
              <h3 className="text-xl font-bold text-gray-900 dark:text-white">
                Update Profile Photo
              </h3>
              <button
                onClick={() => {
                  setShowPhotoModal(false);
                  setPhotoUrl("");
                }}
                className="p-2 hover:bg-gray-100 dark:hover:bg-gray-800 rounded-full transition-colors"
              >
                <X className="h-5 w-5 text-gray-500" />
              </button>
            </div>

            <div className="space-y-4">
              <div className="text-center">
                <div className="w-24 h-24 mx-auto rounded-full border-4 border-gray-200 dark:border-gray-700 overflow-hidden bg-gray-100 dark:bg-gray-800">
                  {photoUrl ? (
                    <img
                      src={photoUrl}
                      alt="Preview"
                      className="w-full h-full object-cover"
                      onError={(e) => {
                        e.currentTarget.src = `https://avatar.vercel.sh/user.svg?text=U`;
                      }}
                    />
                  ) : (
                    <div className="w-full h-full flex items-center justify-center">
                      <ImageIcon className="h-8 w-8 text-gray-400" />
                    </div>
                  )}
                </div>
                <p className="text-sm text-gray-500 mt-2">
                  {photoUrl ? "Preview" : "No image selected"}
                </p>
              </div>

              <div className="space-y-2">
                <Label htmlFor="photoUrl" className="text-sm font-medium">
                  Image URL
                </Label>
                <div className="relative">
                  <Input
                    id="photoUrl"
                    value={photoUrl}
                    onChange={(e) => setPhotoUrl(e.target.value)}
                    placeholder="https://example.com/your-photo.jpg"
                    className="pr-10"
                  />
                  <Upload className="absolute right-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                </div>
                <p className="text-xs text-gray-500">
                  Paste a direct link to your image (JPG, PNG, GIF)
                </p>
              </div>

              <div className="flex gap-3 pt-2">
                <Button
                  variant="outline"
                  onClick={() => {
                    setShowPhotoModal(false);
                    setPhotoUrl("");
                  }}
                  className="flex-1"
                  disabled={isUpdatingPhoto}
                >
                  Cancel
                </Button>
                <Button
                  onClick={handlePhotoUpdate}
                  className="flex-1 bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700 text-white"
                  disabled={isUpdatingPhoto || !photoUrl.trim()}
                >
                  {isUpdatingPhoto ? (
                    <div className="flex items-center justify-center">
                      <svg
                        className="animate-spin -ml-1 mr-2 h-4 w-4 text-white"
                        xmlns="http://www.w3.org/2000/svg"
                        fill="none"
                        viewBox="0 0 24 24"
                      >
                        <circle
                          className="opacity-25"
                          cx="12"
                          cy="12"
                          r="10"
                          stroke="currentColor"
                          strokeWidth="4"
                        ></circle>
                        <path
                          className="opacity-75"
                          fill="currentColor"
                          d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                        ></path>
                      </svg>
                      Updating...
                    </div>
                  ) : (
                    "Update Photo"
                  )}
                </Button>
              </div>

              {/* Upload Note Section */}
              <div className="mt-6 p-4 bg-gradient-to-r from-blue-50 to-purple-50 dark:from-blue-900/20 dark:to-purple-900/20 rounded-xl border border-blue-200 dark:border-blue-800">
                <div className="flex items-start gap-3">
                  <div className="flex-shrink-0 w-8 h-8 bg-blue-100 dark:bg-blue-900 rounded-full flex items-center justify-center">
                    <Upload className="h-4 w-4 text-blue-600 dark:text-blue-400" />
                  </div>
                  <div className="flex-1">
                    <h4 className="text-sm font-semibold text-blue-900 dark:text-blue-100 mb-1">
                      Need to upload an image?
                    </h4>
                    <p className="text-xs text-blue-700 dark:text-blue-300 mb-2">
                      We don't support direct uploads yet. Use our recommended image hosting service:
                    </p>
                    <a
                      href="https://postimages.org/"
                      target="_blank"
                      rel="noopener noreferrer"
                      className="inline-flex items-center gap-2 text-xs font-medium text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-200 transition-colors"
                    >
                      <span className="bg-blue-600 text-white px-2 py-1 rounded-md hover:bg-blue-700 transition-colors">
                        Postimages.org
                      </span>
                      <span>→ Upload & get direct link</span>
                    </a>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      <div className="space-y-6">
        <div>
          <h1 className="text-3xl font-bold">Update Your Profile</h1>
          <p className="text-muted-foreground mt-2">
            Make changes to your profile information.
          </p>
        </div>

        {/* Profile URL Banner - moved to top */}
        {formData.username && (
          <div className="flex justify-center">
            <div className="flex items-center gap-2 bg-green-600/40 backdrop-blur-md border border-green-300/40 text-white px-6 py-2 rounded-full shadow-lg" style={{ minWidth: 320 }}>
              <LinkIcon className="w-5 h-5 mr-2" />
              <span className="font-medium select-all text-base">
                {typeof window !== 'undefined' ? window.location.origin : ''}/<span className="font-bold">{formData.username}</span>
              </span>
              <button
                type="button"
                className="ml-2 p-1 rounded hover:bg-green-800 transition-colors"
                onClick={() => {
                  navigator.clipboard.writeText(`${typeof window !== 'undefined' ? window.location.origin : ''}/${formData.username}`);
                  toast.success("Profile URL copied!");
                }}
              >
                <CopyIcon className="h-5 w-5" />
              </button>
            </div>
          </div>
        )}

        <form
          onSubmit={handleSubmit}
          onKeyDown={handleKeyDown}
          className="space-y-6"
        >
          <div className="space-y-4">
            {/* Current Profile Display */}
            <div className="relative">
              {/* Banner */}
              <div className="w-full h-48 rounded-lg overflow-hidden relative">
                <img
                  src={formData?.bannerURL || "/photos/programming-desk.jpg"}
                  alt="Profile Banner"
                  className="w-full h-full object-cover"
                />
                
                {/* Go Premium Button for non-featured users */}
                {!isFeatured && (
                  <div className="absolute top-4 left-4">
                    <Button
                      onClick={() => navigate("/pricing")}
                      className="bg-yellow-500/90 backdrop-blur-md border border-yellow-300/40 text-black hover:bg-yellow-400/90 transition-all duration-300 shadow-lg hover:shadow-xl"
                      size="sm"
                    >
                      <Crown className="w-4 h-4 mr-2" />
                      Go Premium
                    </Button>
                  </div>
                )}
              </div>

              {/* Profile Photo */}
              <div className="flex justify-center -mt-16 mb-8">
                <div className="relative w-32 h-32 group">
                  <div className="w-32 h-32 rounded-full border-4 border-white dark:border-gray-900 overflow-hidden bg-white dark:bg-gray-900 shadow-xl">
                    <img
                      src={
                        formData?.photoURL ||
                        `https://avatar.vercel.sh/${
                          formData?.username || "user"
                        }.svg?text=${
                          formData?.displayName
                            ? formData.displayName.charAt(0).toUpperCase()
                            : "U"
                        }`
                      }
                      alt={formData?.displayName || "Profile Photo"}
                      className="w-full h-full object-cover"
                    />
                  </div>
                  
                  {/* Camera Icon Overlay */}
                  <button
                    type="button"
                    onClick={() => setShowPhotoModal(true)}
                    className="absolute inset-0 rounded-full bg-black/50 opacity-0 group-hover:opacity-100 transition-opacity duration-200 flex items-center justify-center cursor-pointer"
                  >
                    <div className="bg-white/90 backdrop-blur-sm rounded-full p-2 shadow-lg hover:scale-110 transition-transform">
                      <Camera className="h-5 w-5 text-gray-700" />
                    </div>
                  </button>
                  
                  {/* Featured Badge - positioned perfectly on bottom-right */}
                  {isFeatured && (
                    <div className="absolute -bottom-2 -right-2 z-10">
                      <div className="bg-orange-500 text-white p-2 rounded-full shadow-xl border-2 border-white dark:border-gray-900 hover:scale-110 transition-transform">
                        <Crown className="w-4 h-4" />
                      </div>
                    </div>
                  )}
                </div>
              </div>
            
            {/* Status Indicators */}
            <div className="flex justify-center mb-6">
              <div className="flex items-center gap-3">
                {isFeatured && (
                  <div className="flex items-center gap-2 bg-orange-500/20 backdrop-blur-md border border-orange-300/40 text-orange-700 dark:text-orange-300 px-4 py-2 rounded-full shadow-lg">
                    <Crown className="w-4 h-4" />
                    <span className="font-medium text-sm">Featured Developer</span>
                  </div>
                )}
                {isVerified && (
                  <div className="flex items-center gap-2 bg-blue-500/20 backdrop-blur-md border border-blue-300/40 text-blue-700 dark:text-blue-300 px-4 py-2 rounded-full shadow-lg">
                    <Shield className="w-4 h-4" />
                    <span className="font-medium text-sm">Verified Developer</span>
                  </div>
                )}
                {!isFeatured && !isVerified && (
                  <div className="flex items-center gap-2 bg-gray-500/20 backdrop-blur-md border border-gray-300/40 text-gray-700 dark:text-gray-300 px-4 py-2 rounded-full shadow-lg">
                    <span className="font-medium text-sm">Free Account</span>
                  </div>
                )}
              </div>
            </div>
            {/* Available for Work Section */}
            <div className="p-6 rounded-xl border-2 border-green-300/50 dark:border-green-600/50 bg-green-50/30 dark:bg-green-900/20 backdrop-blur-sm shadow-lg">
              <div className="flex flex-col items-center space-y-4">
                <div className="flex items-center gap-3">
                  <div className="w-3 h-3 rounded-full bg-green-500 animate-pulse"></div>
                  <h3 className="text-lg font-semibold text-green-800 dark:text-green-200">Available for Work</h3>
                </div>
                
                <div className="flex items-center gap-3">
                  <Label htmlFor="availableForWork" className="font-medium text-green-700 dark:text-green-300">Status:</Label>
                  <div className="flex items-center gap-2">
                    <input
                      id="availableForWork"
                      type="checkbox"
                      checked={formData.availableForWork || false}
                      onChange={e => setFormData(prev => ({ ...prev, availableForWork: e.target.checked }))}
                      className="accent-green-500 w-5 h-5"
                    />
                    {formData.availableForWork ? (
                      <span className="flex items-center text-green-600 dark:text-green-400 font-semibold">
                        <span className="h-3 w-3 rounded-full bg-green-500 mr-2 inline-block animate-pulse"></span>
                        Available
                      </span>
                    ) : (
                      <span className="text-gray-500 dark:text-gray-400 font-medium">Not Available</span>
                    )}
                  </div>
                </div>
                
                {formData.availableForWork && (
                  <div className="w-full max-w-md space-y-4 p-4 bg-white/50 dark:bg-gray-800/50 rounded-lg border border-green-200/50 dark:border-green-700/50">
                    <div className="flex flex-col sm:flex-row gap-4 items-center">
                      <div className="flex items-center gap-2">
                        <Label htmlFor="workType" className="font-medium text-green-700 dark:text-green-300">Work Type:</Label>
                        <select
                          id="workType"
                          value={formData.workType || 'freelance'}
                          onChange={e => setFormData(prev => ({ ...prev, workType: e.target.value as 'freelance' | 'full-time' | 'none' }))}
                          className="border border-green-300 dark:border-green-600 rounded px-3 py-1 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 focus:ring-2 focus:ring-green-500 focus:border-green-500 dark:focus:ring-green-400 dark:focus:border-green-400"
                        >
                          <option value="freelance">Freelance</option>
                          <option value="full-time">Full-time</option>
                          <option value="none">Other</option>
                        </select>
                      </div>
                      <div className="flex items-center gap-2">
                        <Label htmlFor="hourlyRate" className="font-medium text-green-700 dark:text-green-300">Rate:</Label>
                        <div className="flex items-center gap-1">
                          <input
                            id="hourlyRate"
                            type="number"
                            min="0"
                            step="1"
                            value={formData.hourlyRate || ''}
                            onChange={e => setFormData(prev => ({ ...prev, hourlyRate: e.target.value }))}
                            placeholder="0"
                            className="border border-green-300 dark:border-green-600 rounded px-3 py-1 w-20 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 focus:ring-2 focus:ring-green-500 focus:border-green-500 dark:focus:ring-green-400 dark:focus:border-green-400"
                          />
                          <span className="text-green-600 dark:text-green-400 text-sm font-medium">USD/hr</span>
                        </div>
                      </div>
                    </div>
                  </div>
                )}
              </div>
            </div>
            </div>

            <div>
              <Label>Profile Banner</Label>
              <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-4 mt-2">
                {bannerOptions?.map((banner: BannerOption) => (
                  <div
                    key={banner.id}
                    className={`relative aspect-video cursor-pointer rounded-lg overflow-hidden border-2 ${
                      formData?.bannerURL === banner.url
                        ? "border-primary"
                        : "border-transparent"
                    }`}
                    onClick={() => handleBannerSelect(banner)}
                  >
                    <img
                      src={banner.url}
                      alt={banner.name}
                      className="w-full h-full object-cover"
                    />
                    {formData?.bannerURL === banner.url && (
                      <div className="absolute inset-0 bg-black/20 flex items-center justify-center">
                        <Check className="h-8 w-8 text-white" />
                      </div>
                    )}
                  </div>
                ))}
              </div>

              <h3 className="text-lg font-semibold mt-8 mb-4">
                Premium Banners
              </h3>
              <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-4">
                {premiumBannerOptions?.map((banner: BannerOption) => (
                  <div
                    key={banner.id}
                    className={`relative aspect-video rounded-lg overflow-hidden border-2 ${
                      formData?.bannerURL === banner.url
                        ? "border-primary"
                        : "border-transparent"
                    } ${
                      !isFeatured
                        ? "cursor-not-allowed opacity-60"
                        : "cursor-pointer"
                    }`}
                    onClick={() => isFeatured && handleBannerSelect(banner)}
                  >
                    <img
                      src={banner.url}
                      alt={banner.name}
                      className="w-full h-full object-cover"
                    />
                    {formData?.bannerURL === banner.url && (
                      <div className="absolute inset-0 bg-black/20 flex items-center justify-center">
                        <Check className="h-8 w-8 text-white" />
                      </div>
                    )}
                    <div className="absolute top-2 right-2 bg-yellow-500 text-white px-2 py-1 rounded-full text-xs">
                      Premium
                    </div>
                    {!isFeatured && (
                      <div className="absolute inset-0 bg-black/50 flex flex-col items-center justify-center text-white">
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          className="h-8 w-8 mb-2"
                          fill="none"
                          viewBox="0 0 24 24"
                          stroke="currentColor"
                        >
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth={2}
                            d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"
                          />
                        </svg>
                        <span className="text-sm font-medium">
                          Upgrade to Featured
                        </span>
                      </div>
                    )}
                  </div>
                ))}
              </div>
            </div>

            <div>
              <Label htmlFor="displayName">Display Name</Label>
              <Input
                id="displayName"
                name="displayName"
                value={formData.displayName}
                onChange={handleChange}
                required
                placeholder="Your display name"
                className={cn(
                  getFieldError("displayName")?.type === "error" &&
                    "border-red-500"
                )}
              />
              {getFieldError("displayName") && (
                <p className="text-red-500 text-sm mt-1">
                  {getFieldError("displayName")?.message}
                </p>
              )}
            </div>

            <div>
              <Label htmlFor="username">Username</Label>
              <Input
                id="username"
                name="username"
                value={formData.username}
                onChange={handleUsernameChange}
                required
                placeholder="Your username"
                className={cn(
                  getFieldError("username")?.type === "error" &&
                    "border-red-500",
                  getFieldError("username")?.type === "success" &&
                    "border-green-500"
                )}
              />
              {getFieldError("username") && (
                <p
                  className={cn(
                    "text-sm mt-1",
                    getFieldError("username")?.type === "error" &&
                      "text-red-500",
                    getFieldError("username")?.type === "success" &&
                      "text-green-500"
                  )}
                >
                  {getFieldError("username")?.message}
                </p>
              )}
            </div>

            <div>
              <Label htmlFor="bio">Bio</Label>
              <Textarea
                id="bio"
                name="bio"
                value={formData.bio}
                onChange={handleChange}
                placeholder="Tell us about yourself"
                rows={4}
              />
            </div>

            <div>
              <Label htmlFor="country">Country</Label>
              <CountrySelect
                value={formData.country}
                onValueChange={(value) => {
                  setFormData((prev) => ({ ...prev, country: value }));
                  clearError("country");
                }}
              />
              {getFieldError("country") && (
                <p className="text-red-500 text-sm mt-1">
                  {getFieldError("country")?.message}
                </p>
              )}
            </div>

            <div>
              <Label htmlFor="role">Role</Label>
              <Select
                value={formData.role}
                onValueChange={(value: string) => {
                  handleSelectChange("role", value);
                  clearError("role");
                }}
                placeholder="Select your role"
                className={cn(
                  getFieldError("role")?.type === "error" && "border-red-500"
                )}
              >
                {roleOptions.map((role) => (
                  <SelectItem key={role} value={role}>
                    {role}
                  </SelectItem>
                ))}
              </Select>
              {getFieldError("role") && (
                <p className="text-red-500 text-sm mt-1">
                  {getFieldError("role")?.message}
                </p>
              )}
            </div>

            <div>
              <Label htmlFor="seniorityLevel">Seniority Level</Label>
              <Select
                value={formData.seniorityLevel}
                onValueChange={(value: string) => {
                  handleSelectChange("seniorityLevel", value);
                  clearError("seniorityLevel");
                }}
                placeholder="Select your seniority level"
                className={cn(
                  getFieldError("seniorityLevel")?.type === "error" &&
                    "border-red-500"
                )}
              >
                {seniorityOptions.map((level) => (
                  <SelectItem key={level} value={level}>
                    {level}
                  </SelectItem>
                ))}
              </Select>
              {getFieldError("seniorityLevel") && (
                <p className="text-red-500 text-sm mt-1">
                  {getFieldError("seniorityLevel")?.message}
                </p>
              )}
            </div>

            <div>
              <Label htmlFor="techStack">Tech Stack</Label>
              <Popover>
                <PopoverTrigger asChild>
                  <Button
                    variant="outline"
                    role="combobox"
                    className={cn(
                      "w-full justify-between",
                      getFieldError("techStack")?.type === "error" &&
                        "border-red-500"
                    )}
                  >
                    Select technologies...
                    <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-full p-0">
                  <Command>
                    <CommandInput placeholder="Search technologies..." />
                    <CommandEmpty>No technology found.</CommandEmpty>
                    <CommandGroup className="max-h-64 overflow-auto">
                      {techStackOptions.map((tech) => (
                        <CommandItem
                          key={tech}
                          onSelect={() => {
                            if (
                              !formData.techStack.includes(tech) &&
                              formData.techStack.length >= 15
                            ) {
                              return;
                            }
                            handleTechStackChange(tech);
                            clearError("techStack");
                          }}
                        >
                          <Check
                            className={cn(
                              "mr-2 h-4 w-4",
                              formData.techStack.includes(tech)
                                ? "opacity-100"
                                : "opacity-0"
                            )}
                          />
                          {tech}
                        </CommandItem>
                      ))}
                    </CommandGroup>
                  </Command>
                </PopoverContent>
              </Popover>
              {getFieldError("techStack") && (
                <p className="text-red-500 text-sm mt-1">
                  {getFieldError("techStack")?.message}
                </p>
              )}
              <div className="flex flex-wrap gap-2 mt-2">
                {formData?.techStack?.map((tech) => (
                  <Badge
                    key={tech}
                    variant="secondary"
                    className="flex items-center gap-1"
                  >
                    {tech}
                    <button
                      type="button"
                      onClick={() => removeTechStack(tech)}
                      className="ml-1 ring-offset-background rounded-full outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2"
                    >
                      <X className="h-3 w-3" />
                      <span className="sr-only">Remove {tech}</span>
                    </button>
                  </Badge>
                ))}
              </div>
            </div>

            <div>
              <Label htmlFor="website">Website</Label>
              <Input
                id="website"
                name="website"
                value={formData.website}
                onChange={handleChange}
                placeholder="Your website"
                type="url"
                required
                className={cn(
                  getFieldError("website")?.type === "error" && "border-red-500"
                )}
              />
              {getFieldError("website") && (
                <p className="text-red-500 text-sm mt-1">
                  {getFieldError("website")?.message}
                </p>
              )}
            </div>

            <div>
              <Label htmlFor="githubUrl">GitHub</Label>
              <Input
                id="githubUrl"
                name="githubUrl"
                value={formData.githubUrl}
                onChange={handleChange}
                placeholder="Your GitHub username"
                className={cn(
                  getFieldError("githubUrl")?.type === "error" &&
                    "border-red-500"
                )}
              />
              {getFieldError("githubUrl") && (
                <p className="text-red-500 text-sm mt-1">
                  {getFieldError("githubUrl")?.message}
                </p>
              )}
            </div>

            <div>
              <Label htmlFor="twitterUrl">Twitter</Label>
              <Input
                id="twitterUrl"
                name="twitterUrl"
                value={formData.twitterUrl}
                onChange={handleChange}
                placeholder="Your Twitter username"
                className={cn(
                  getFieldError("twitterUrl")?.type === "error" &&
                    "border-red-500"
                )}
              />
              {getFieldError("twitterUrl") && (
                <p className="text-red-500 text-sm mt-1">
                  {getFieldError("twitterUrl")?.message}
                </p>
              )}
            </div>

            <div>
              <Label htmlFor="linkedinUrl">LinkedIn</Label>
              <Input
                id="linkedinUrl"
                name="linkedinUrl"
                value={formData.linkedinUrl}
                onChange={handleChange}
                placeholder="Your LinkedIn username"
                className={cn(
                  getFieldError("linkedinUrl")?.type === "error" &&
                    "border-red-500"
                )}
              />
              {getFieldError("linkedinUrl") && (
                <p className="text-red-500 text-sm mt-1">
                  {getFieldError("linkedinUrl")?.message}
                </p>
              )}
            </div>
          </div>

          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <h2 className="text-xl font-semibold">Project Showcase</h2>
              <Button
                type="button"
                variant="outline"
                onClick={() => {
                  setCurrentProject({
                    id: "",
                    title: "",
                    description: "",
                    url: "",
                    imageUrl: "",
                    technologies: [],
                  });
                  setEditingProjectId(null);
                  setShowProjectForm(!showProjectForm);
                }}
                className="flex items-center gap-2"
              >
                <Plus className="h-4 w-4" />
                Add Project
              </Button>
            </div>

            {showProjectForm && (
              <div className="space-y-4 p-4 border rounded-lg">
                <div>
                  <Label htmlFor="projectTitle">Project Title</Label>
                  <Input
                    id="projectTitle"
                    name="title"
                    value={currentProject.title}
                    onChange={handleProjectChange}
                    placeholder="Enter project title"
                    required
                  />
                </div>

                <div>
                  <Label htmlFor="projectDescription">
                    Project Description
                  </Label>
                  <Textarea
                    id="projectDescription"
                    name="description"
                    value={currentProject.description}
                    onChange={handleProjectChange}
                    placeholder="Describe your project"
                    rows={4}
                    required
                  />
                </div>

                <div>
                  <Label htmlFor="projectUrl">Project URL</Label>
                  <Input
                    id="projectUrl"
                    name="url"
                    value={currentProject.url}
                    onChange={handleProjectChange}
                    placeholder="https://your-project.com"
                    type="url"
                  />
                </div>

                <div>
                  <Label htmlFor="projectImageUrl">Project Image URL</Label>
                  <Input
                    id="projectImageUrl"
                    name="imageUrl"
                    value={currentProject.imageUrl}
                    onChange={handleProjectChange}
                    placeholder="https://postimages.org/your-project-image"
                    type="url"
                  />
                  <div className="mt-2 p-3 bg-blue-50 border border-blue-200 rounded-lg">
                    <div className="flex items-start gap-2">
                      <div className="flex-shrink-0 mt-0.5">
                        <svg className="w-4 h-4 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
                          <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
                        </svg>
                      </div>
                      <div className="flex-1">
                        <p className="text-sm text-blue-800 font-medium">Need to host an image?</p>
                        <p className="text-xs text-blue-700 mt-1">
                          We don't support direct image uploads yet. Use{" "}
                          <a
                            href="https://postimages.org/"
                            target="_blank"
                            rel="noopener noreferrer"
                            className="underline hover:text-blue-900 font-medium"
                          >
                            Postimages.org
                          </a>{" "}
                          to upload your project screenshot and paste the direct link here.
                        </p>
                      </div>
                    </div>
                  </div>
                </div>

                <div>
                  <Label htmlFor="projectTechStack">Technologies Used</Label>
                  <Popover
                    open={projectTechStackOpen}
                    onOpenChange={setProjectTechStackOpen}
                  >
                    <PopoverTrigger asChild>
                      <Button
                        variant="outline"
                        role="combobox"
                        aria-expanded={projectTechStackOpen}
                        className="w-full justify-between"
                      >
                        Select technologies...
                        <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
                      </Button>
                    </PopoverTrigger>
                    <PopoverContent className="w-full p-0">
                      <Command>
                        <CommandInput placeholder="Search technologies..." />
                        <CommandEmpty>No technology found.</CommandEmpty>
                        <CommandGroup className="max-h-64 overflow-auto">
                          {techStackOptions.map((tech) => (
                            <CommandItem
                              key={tech}
                              onSelect={() =>
                                handleProjectTechStackChange(tech)
                              }
                            >
                              <Check
                                className={cn(
                                  "mr-2 h-4 w-4",
                                  currentProject.technologies?.includes(tech)
                                    ? "opacity-100"
                                    : "opacity-0"
                                )}
                              />
                              {tech}
                            </CommandItem>
                          ))}
                        </CommandGroup>
                      </Command>
                    </PopoverContent>
                  </Popover>
                  <div className="flex flex-wrap gap-2 mt-2">
                    {currentProject.technologies?.map((tech) => (
                      <Badge
                        key={tech}
                        variant="secondary"
                        className="flex items-center gap-1"
                      >
                        {tech}
                        <button
                          type="button"
                          onClick={() => removeProjectTech(tech)}
                          className="ml-1 ring-offset-background rounded-full outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2"
                        >
                          <X className="h-3 w-3" />
                          <span className="sr-only">Remove {tech}</span>
                        </button>
                      </Badge>
                    ))}
                  </div>
                </div>

                <div className="flex justify-end gap-2">
                  <Button
                    type="button"
                    variant="outline"
                    onClick={() => {
                      setShowProjectForm(false);
                      setEditingProjectId(null);
                      setCurrentProject({
                        id: "",
                        title: "",
                        description: "",
                        url: "",
                        imageUrl: "",
                        technologies: [],
                      });
                    }}
                  >
                    Cancel
                  </Button>
                  <Button type="button" onClick={addProject}>
                    {editingProjectId ? "Update Project" : "Add Project"}
                  </Button>
                </div>
              </div>
            )}

            {formData?.projects?.length > 0 && (
              <div className="space-y-4">
                {formData.projects.map((project, index) => {
                  console.log(`Rendering project ${index}:`, { id: project.id, title: project.title });
                  return (
                    <div
                      key={`${project.id}-${index}`}
                      className="p-4 border rounded-lg space-y-2"
                    >
                    <div className="flex items-start gap-4">
                      {project.imageUrl && (
                        <div className="flex-shrink-0">
                          <img
                            src={project.imageUrl}
                            alt={project.title}
                            className="w-24 h-24 object-cover rounded-lg border"
                            onError={(e) => {
                              e.currentTarget.style.display = 'none';
                            }}
                          />
                        </div>
                      )}
                      <div className="flex-1 min-w-0">
                        <div className="flex items-start justify-between">
                          <div className="flex-1">
                            <h3 className="font-semibold">{project.title}</h3>
                            <p className="text-sm text-muted-foreground">
                              {project.description}
                            </p>
                            {project.url && (
                              <a
                                href={project.url}
                                target="_blank"
                                rel="noopener noreferrer"
                                className="text-sm text-blue-500 hover:underline"
                              >
                                View Project
                              </a>
                            )}
                          </div>
                          <div className="flex items-center gap-1">
                            <Button
                              type="button"
                              variant="ghost"
                              size="icon"
                              onClick={() => editProject(project)}
                              className="text-blue-500 hover:text-blue-700 hover:bg-blue-50"
                            >
                              <Edit className="h-4 w-4" />
                              <span className="sr-only">Edit project</span>
                            </Button>
                            <Button
                              type="button"
                              variant="ghost"
                              size="icon"
                              onClick={() => removeProject(project.id)}
                              className="text-red-500 hover:text-red-700 hover:bg-red-50"
                            >
                              <Trash2 className="h-4 w-4" />
                              <span className="sr-only">Remove project</span>
                            </Button>
                          </div>
                        </div>
                        <div className="flex flex-wrap gap-2 mt-2">
                          {project.technologies?.map((tech) => (
                            <Badge key={tech} variant="secondary">
                              {tech}
                            </Badge>
                          ))}
                        </div>
                      </div>
                    </div>
                  </div>
                  );
                })}
              </div>
            )}
          </div>

          <Button
            type="submit"
            className="w-full"
            disabled={loading || isValidating}
          >
            {loading ? (
              <div className="flex items-center justify-center">
                <svg
                  className="animate-spin -ml-1 mr-3 h-5 w-5 text-white"
                  xmlns="http://www.w3.org/2000/svg"
                  fill="none"
                  viewBox="0 0 24 24"
                >
                  <circle
                    className="opacity-25"
                    cx="12"
                    cy="12"
                    r="10"
                    stroke="currentColor"
                    strokeWidth="4"
                  ></circle>
                  <path
                    className="opacity-75"
                    fill="currentColor"
                    d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                  ></path>
                </svg>
                Updating Profile...
              </div>
            ) : isValidating ? (
              "Validating..."
            ) : (
              "Update Profile"
            )}
          </Button>
        </form>
      </div>
    </div>
  );
}
