import {
  signInWithPopup,
  signOut as firebaseSignOut,
  onAuthStateChanged,
  type User as FirebaseUser
} from 'firebase/auth';
import { doc, getDoc, setDoc, deleteDoc } from 'firebase/firestore';
import { auth, googleProvider, db } from '@/config/firebase';
import { getUserByUid, getUserByEmail } from './firebase-db';
import type { UserProfile } from './firebase-db';

export interface AuthUser extends FirebaseUser {
  hasProfile?: boolean;
}

// Sign in with Google
export const signInWithGoogle = async (): Promise<{ user: AuthUser; hasProfile: boolean } | null> => {
  try {
    const result = await signInWithPopup(auth, googleProvider);
    const user = result.user;

    console.log('Google sign-in successful:', {
      uid: user.uid,
      email: user.email,
      displayName: user.displayName,
      photoURL: user.photoURL
    });

    // Check if user profile exists by UID (primary check)
    console.log('Checking for existing profile by UID:', user.uid);
    let userProfile = await getUserByUid(user.uid);
    let hasProfile = !!userProfile;

    console.log('Profile found by UID:', hasProfile, userProfile);

    // If no profile found by UID and user has email, check by email (for migration purposes)
    if (!userProfile && user.email) {
      console.log('No profile found by UID, checking by email:', user.email);
      const profileByEmail = await getUserByEmail(user.email);

      if (profileByEmail && profileByEmail.uid !== user.uid) {
        console.log('Found profile by email, migrating to new UID');
        // Migrate the profile to the correct UID
        await setDoc(doc(db, 'users', user.uid), {
          ...profileByEmail,
          uid: user.uid,
          email: user.email,
          photoURL: user.photoURL,
          updatedAt: Date.now()
        });

        // Delete the old document
        try {
          const oldDocRef = doc(db, 'users', profileByEmail.uid);
          const oldDoc = await getDoc(oldDocRef);
          if (oldDoc.exists()) {
            await deleteDoc(oldDocRef);
          }
        } catch (error) {
          console.warn('Could not delete old user document:', error);
        }

        userProfile = await getUserByUid(user.uid);
        hasProfile = !!userProfile;
        console.log('Profile migrated successfully:', hasProfile);
      }
    }

    console.log('Final auth result:', { hasProfile, userProfile: !!userProfile });

    return {
      user: { ...user, hasProfile },
      hasProfile
    };
  } catch (error) {
    console.error('Error signing in with Google:', error);
    throw error;
  }
};

// Sign out
export const signOut = async (): Promise<void> => {
  try {
    await firebaseSignOut(auth);
  } catch (error) {
    console.error('Error signing out:', error);
    throw error;
  }
};

// Get current user profile from Firestore
export const getCurrentUserProfile = async (): Promise<UserProfile | null> => {
  try {
    const user = auth.currentUser;
    if (!user) return null;

    // Use UID to get user profile (more reliable than email)
    return await getUserByUid(user.uid);
  } catch (error) {
    console.error('Error getting current user profile:', error);
    return null;
  }
};

// Check if current user has a profile
export const currentUserHasProfile = async (): Promise<boolean> => {
  try {
    const user = auth.currentUser;
    if (!user) return false;

    // Check by UID instead of email
    const userProfile = await getUserByUid(user.uid);
    return !!userProfile;
  } catch (error) {
    console.error('Error checking if user has profile:', error);
    return false;
  }
};

// Auth state observer
export const onAuthStateChange = (callback: (user: AuthUser | null) => void) => {
  return onAuthStateChanged(auth, async (user) => {
    if (user) {
      const hasProfile = await currentUserHasProfile();
      callback({ ...user, hasProfile });
    } else {
      callback(null);
    }
  });
};

// Get auth user with profile status
export const getAuthUserWithProfile = async (user: FirebaseUser): Promise<AuthUser> => {
  const hasProfile = await currentUserHasProfile();
  return { ...user, hasProfile };
};
